import React, { useRef, useEffect, useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Animated,
  Platform,
  StatusBar,
  Image,
  Dimensions,
  Modal,
  Easing
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import BottomNavigation from './components/BottomNavigation';
import CustomStatusBar from './components/CustomStatusBar';
import { useUser } from './context/UserContext';

const { width } = Dimensions.get('window');

const Dashboard = () => {
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideUpAnim = useRef(new Animated.Value(50)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const modalSlideAnim = useRef(new Animated.Value(300)).current;
  const modalOpacityAnim = useRef(new Animated.Value(0)).current;

  const router = useRouter();
  const { userData, toggleHidePersonalInfo } = useUser();

  // State management
  const [selectedMood, setSelectedMood] = useState(null);
  const [showDailyInsights, setShowDailyInsights] = useState(true);
  const [showInsightsModal, setShowInsightsModal] = useState(false);

  // Mood options exactly matching the reference design
  const moodOptions = [
    { emoji: '😔', label: 'Sad' },
    { emoji: '😐', label: 'Meh' },
    { emoji: '🙂', label: 'Okay' },
    { emoji: '😊', label: 'Good' },
    { emoji: '😄', label: 'Great' }
  ];

  // Sample appointment data
  const upcomingAppointment = {
    doctor: 'Dr. Abie Rufino',
    specialty: 'Psychometrician',
    date: 'May 25, 2025',
    time: '8:00 am',
    initial: 'A'
  };

  useEffect(() => {
    // Enhanced entrance animations with staggered effect
    Animated.stagger(200, [
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.spring(slideUpAnim, {
        toValue: 0,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Enhanced notification pulse animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 1500,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.ease),
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.ease),
        }),
      ])
    ).start();
  }, []);

  // Mood selection animation
  const moodScaleAnims = moodOptions.map(() => useRef(new Animated.Value(1)).current);

  const animateMoodSelection = (index) => {
    // Enhanced mood selection animation with haptic feedback
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 1.2,
        duration: 150,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.spring(scaleAnim, {
        toValue: 1.1,
        tension: 300,
        friction: 6,
        useNativeDriver: true,
      })
    ]).start();
  };

  const handleMoodSelect = (index) => {
    setSelectedMood(index);
    animateMoodSelection(index);

    // Add subtle haptic feedback for better UX
    // Note: You can add Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light) here
  };

  const handleSaveMood = () => {
    if (selectedMood !== null) {
      router.push({
        pathname: '/mood-journal',
        params: {
          mood: moodOptions[selectedMood].emoji,
          moodIndex: selectedMood
        }
      });
    }
  };

  const handleProfilePress = () => {
    router.push('/user-profile-redesigned');
  };

  const getCurrentDate = () => {
    const today = new Date();
    return today.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Daily Insights Modal Animation
  const showInsightsModalWithAnimation = () => {
    setShowInsightsModal(true);
    Animated.parallel([
      Animated.timing(modalOpacityAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(modalSlideAnim, {
        toValue: 0,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      })
    ]).start();
  };

  const hideInsightsModalWithAnimation = () => {
    Animated.parallel([
      Animated.timing(modalOpacityAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(modalSlideAnim, {
        toValue: 300,
        duration: 200,
        useNativeDriver: true,
      })
    ]).start(() => {
      setShowInsightsModal(false);
      setShowDailyInsights(false);
    });
  };

  return (
    <View style={styles.container}>
      <StatusBar
        translucent={true}
        backgroundColor="transparent"
        barStyle="light-content"
      />

      <SafeAreaView style={styles.safeArea}>
        {/* Header with Gradient - Matching reference design exactly */}
        <LinearGradient
          colors={['#7BA05B', '#9BC76D']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <View style={styles.userInfoContainer}>
              <TouchableOpacity
                style={styles.profilePicture}
                onPress={handleProfilePress}
              >
                <Text style={styles.profileInitial}>
                  {(userData.firstName || 'L').charAt(0)}
                </Text>
              </TouchableOpacity>
              <Text style={styles.userName}>
                {userData.hidePersonalInfo ? '2021*******' : (userData.controlNumber || '2021*******')}
              </Text>
            </View>
            <TouchableOpacity style={styles.notificationButton}>
              <View style={styles.notificationBell}>
                <View style={styles.notificationIconContainer}>
                  <Text style={styles.bellIcon}>🔔</Text>
                  <Animated.View
                    style={[
                      styles.notificationBadge,
                      { transform: [{ scale: pulseAnim }] }
                    ]}
                  />
                </View>
              </View>
            </TouchableOpacity>
          </View>
        </LinearGradient>

        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Mood Tracker Card - Exact match to reference design */}
          <Animated.View
            style={[
              styles.moodCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <View style={styles.moodCardContent}>
              <Text style={styles.moodTitle}>How are you feeling today?</Text>
              <View style={styles.moodDateContainer}>
                <Text style={styles.moodSubtitle}>Today's mood</Text>
                <Text style={styles.moodDate}>{getCurrentDate()}</Text>
              </View>

              <View style={styles.moodOptionsContainer}>
                {moodOptions.map((mood, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.moodOptionWrapper}
                    onPress={() => handleMoodSelect(index)}
                    activeOpacity={0.7}
                  >
                    <View style={[
                      styles.moodEmojiContainer,
                      selectedMood === index && styles.selectedMoodEmojiContainer
                    ]}>
                      <Text style={styles.moodEmoji}>
                        {mood.emoji}
                      </Text>
                    </View>
                    <Text style={[
                      styles.moodLabel,
                      selectedMood === index && styles.selectedMoodLabel
                    ]}>
                      {mood.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <TouchableOpacity
                style={[
                  styles.saveMoodButton,
                  selectedMood === null && styles.saveMoodButtonDisabled
                ]}
                onPress={handleSaveMood}
                disabled={selectedMood === null}
                activeOpacity={0.8}
              >
                <Text style={styles.saveMoodButtonText}>Save Mood</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>

          {/* Daily Insights Card - Redesigned to match first image */}
          {showDailyInsights && (
            <Animated.View
              style={[
                styles.insightsCard,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideUpAnim }]
                }
              ]}
            >
              <TouchableOpacity
                style={styles.insightsCardTouchable}
                onPress={showInsightsModalWithAnimation}
                activeOpacity={0.95}
              >
                <View style={styles.insightsHeader}>
                  <Text style={styles.insightsTitle}>Daily Insights</Text>
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={() => setShowDailyInsights(false)}
                  >
                    <Text style={styles.closeButtonText}>✕</Text>
                  </TouchableOpacity>
                </View>
                <View style={styles.insightsPreview}>
                  <View style={styles.insightsAccent} />
                  <Text style={styles.insightsText}>
                    It seems like you are stress in the past few weeks.{'\n'}
                    Here are some recommendations:
                  </Text>
                </View>
                <View style={styles.recommendationsList}>
                  <Text style={styles.recommendationItem}>• Take a deept breath</Text>
                  <Text style={styles.recommendationItem}>• Write down what is bothering you</Text>
                  <Text style={styles.recommendationItem}>• Do something kind to yourself</Text>
                </View>
              </TouchableOpacity>
            </Animated.View>
          )}

          {/* Upcoming Appointment Card - Redesigned */}
          <Animated.View
            style={[
              styles.appointmentCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <Text style={styles.appointmentTitle}>Upcoming Appointment</Text>
            <View style={styles.appointmentContent}>
              <View style={styles.doctorInfo}>
                <View style={styles.doctorAvatar}>
                  <Text style={styles.doctorInitial}>{upcomingAppointment.initial}</Text>
                </View>
                <View style={styles.doctorDetails}>
                  <Text style={styles.doctorName}>{upcomingAppointment.doctor}</Text>
                  <Text style={styles.doctorSpecialty}>{upcomingAppointment.specialty}</Text>
                </View>
              </View>
              <View style={styles.appointmentTime}>
                <Text style={styles.appointmentDate}>{upcomingAppointment.date}</Text>
                <Text style={styles.appointmentTimeText}>{upcomingAppointment.time}</Text>
              </View>
            </View>
            <TouchableOpacity
              style={styles.rescheduleButton}
              onPress={() => router.push('/reschedule-appointment')}
            >
              <Text style={styles.rescheduleButtonText}>Reschedule</Text>
            </TouchableOpacity>
          </Animated.View>

          {/* AI Mental Health Assistant Card - Matching reference design */}
          <Animated.View
            style={[
              styles.aiAssistantCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <View style={styles.aiAssistantContent}>
              <View style={styles.aiAssistantHeader}>
                <View style={styles.aiIcon}>
                  <Text style={styles.aiIconText}>💬</Text>
                </View>
                <Text style={styles.aiAssistantTitle}>AI Mental Health Assistant</Text>
              </View>
              <Text style={styles.aiAssistantSubtitle}>
                Feeling overwhelmed or need someone to talk to?
              </Text>
              <Text style={styles.aiAssistantDescription}>
                Our AI assistant is here to listen, provide support, and offer guidance 24/7
              </Text>
              <TouchableOpacity
                style={styles.chatButton}
                onPress={() => router.push('/ai-chatbot')}
              >
                <Text style={styles.chatButtonText}>Chat with Aira</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>

          {/* Mental Health Assessment Card - Matching teal gradient from reference */}
          <Animated.View
            style={[
              styles.assessmentCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <LinearGradient
              colors={['#4A9B8E', '#6BB6A7']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.assessmentGradient}
            >
              <Text style={styles.assessmentTitle}>Mental Health Assessment</Text>
              <Text style={styles.assessmentDescription}>
                Take a quuick assessment to better understand your current mental health state and get personalized recommendation
              </Text>
              <TouchableOpacity
                style={styles.startAssessmentButton}
                onPress={() => router.push('/mental-assessment')}
              >
                <Text style={styles.startAssessmentButtonText}>Start self-assessment</Text>
              </TouchableOpacity>
            </LinearGradient>
          </Animated.View>

          {/* Stress Management Card */}
          <Animated.View
            style={[
              styles.stressManagementCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <View style={styles.stressManagementContent}>
              <View style={styles.stressManagementHeader}>
                <View style={styles.stressIcon}>
                  <Text style={styles.stressIconText}>🧘</Text>
                </View>
                <Text style={styles.stressManagementTitle}>Stress Management</Text>
              </View>
              <Text style={styles.stressManagementDescription}>
                Track your thoughts, feelings, and activities to identify patterns and improve your mental well-being.
              </Text>
              <TouchableOpacity
                style={styles.startJournalingButton}
                onPress={() => router.push('/mood-journal')}
              >
                <Text style={styles.startJournalingButtonText}>Start journaling</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </ScrollView>

        {/* Daily Insights Modal - Matching the design from first image */}
        <Modal
          visible={showInsightsModal}
          transparent={true}
          animationType="none"
          onRequestClose={hideInsightsModalWithAnimation}
        >
          <Animated.View
            style={[
              styles.modalOverlay,
              { opacity: modalOpacityAnim }
            ]}
          >
            <TouchableOpacity
              style={styles.modalBackdrop}
              activeOpacity={1}
              onPress={hideInsightsModalWithAnimation}
            />
            <Animated.View
              style={[
                styles.insightsModal,
                {
                  transform: [{ translateY: modalSlideAnim }]
                }
              ]}
            >
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Daily Insights</Text>
                <TouchableOpacity
                  style={styles.modalCloseButton}
                  onPress={hideInsightsModalWithAnimation}
                >
                  <Text style={styles.modalCloseText}>✕</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.modalContent}>
                <View style={styles.modalInsightsAccent} />
                <Text style={styles.modalInsightsText}>
                  It seems like you are stress in the past few weeks.{'\n'}
                  Here are some recommendations:
                </Text>
                <View style={styles.modalRecommendationsList}>
                  <Text style={styles.modalRecommendationItem}>• Take a deept breath</Text>
                  <Text style={styles.modalRecommendationItem}>• Write down what is bothering you</Text>
                  <Text style={styles.modalRecommendationItem}>• Do something kind to yourself</Text>
                </View>
              </View>
            </Animated.View>
          </Animated.View>
        </Modal>

        {/* Bottom Navigation */}
        <BottomNavigation />
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  safeArea: {
    flex: 1,
  },
  headerGradient: {
    paddingTop: Platform.OS === 'ios' ? 60 : StatusBar.currentHeight + 30,
    paddingBottom: 40,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profilePicture: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#D4A574',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  profileInitial: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  userName: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  notificationButton: {
    padding: 4,
  },
  notificationBell: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  notificationIconContainer: {
    position: 'relative',
  },
  bellIcon: {
    fontSize: 18,
    color: '#FFFFFF',
  },
  notificationBadge: {
    position: 'absolute',
    top: 2,
    right: 2,
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#FF4757',
    borderWidth: 1,
    borderColor: '#FFFFFF',
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 120,
  },
  moodCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    marginTop: -25,
  },
  moodCardContent: {
    padding: 20,
  },
  moodTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 8,
  },
  moodDateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  moodSubtitle: {
    fontSize: 14,
    color: '#7F8C8D',
    fontWeight: '500',
  },
  moodDate: {
    fontSize: 14,
    color: '#7F8C8D',
    fontWeight: '600',
  },
  moodOptionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
    paddingHorizontal: 10,
  },
  moodOptionWrapper: {
    alignItems: 'center',
    flex: 1,
  },
  moodEmojiContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedMoodEmojiContainer: {
    backgroundColor: '#7BA05B',
    borderColor: '#7BA05B',
  },
  moodEmoji: {
    fontSize: 24,
  },
  moodLabel: {
    fontSize: 12,
    color: '#7F8C8D',
    fontWeight: '600',
    textAlign: 'center',
  },
  selectedMoodLabel: {
    color: '#7BA05B',
    fontWeight: 'bold',
  },
  saveMoodButton: {
    backgroundColor: '#7BA05B',
    borderRadius: 25,
    paddingVertical: 14,
    alignItems: 'center',
  },
  saveMoodButtonDisabled: {
    backgroundColor: '#E0E0E0',
  },
  saveMoodButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  insightsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  insightsCardTouchable: {
    padding: 20,
  },
  insightsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 14,
  },
  insightsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  closeButton: {
    padding: 6,
    borderRadius: 12,
    backgroundColor: '#F8F9FA',
  },
  closeButtonText: {
    fontSize: 14,
    color: '#7F8C8D',
    fontWeight: '600',
  },
  insightsPreview: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 14,
  },
  insightsAccent: {
    width: 3,
    height: 35,
    backgroundColor: '#7BA05B',
    borderRadius: 2,
    marginRight: 14,
  },
  insightsText: {
    fontSize: 14,
    color: '#5D6D7E',
    lineHeight: 20,
    flex: 1,
    fontStyle: 'italic',
  },
  recommendationsList: {
    paddingLeft: 17,
  },
  recommendationItem: {
    fontSize: 14,
    color: '#5D6D7E',
    lineHeight: 22,
    marginBottom: 5,
  },
  appointmentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    marginBottom: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  appointmentTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 16,
  },
  appointmentContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 18,
  },
  doctorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  doctorAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#D4A574',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  doctorInitial: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  doctorDetails: {
    flex: 1,
  },
  doctorName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 4,
  },
  doctorSpecialty: {
    fontSize: 14,
    color: '#7F8C8D',
    fontWeight: '500',
  },
  appointmentTime: {
    alignItems: 'flex-end',
  },
  appointmentDate: {
    fontSize: 14,
    color: '#2C3E50',
    fontWeight: '600',
    marginBottom: 4,
  },
  appointmentTimeText: {
    fontSize: 14,
    color: '#7F8C8D',
    fontWeight: '500',
  },
  rescheduleButton: {
    backgroundColor: '#9BC76D',
    borderRadius: 25,
    paddingVertical: 12,
    alignItems: 'center',
  },
  rescheduleButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: 'bold',
  },
  aiAssistantCard: {
    backgroundColor: '#E8F5E8',
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  aiAssistantContent: {
    padding: 20,
  },
  aiAssistantHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  aiIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  aiIconText: {
    fontSize: 20,
  },
  aiAssistantTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  aiAssistantSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 8,
    lineHeight: 22,
  },
  aiAssistantDescription: {
    fontSize: 14,
    color: '#5D6D7E',
    lineHeight: 20,
    marginBottom: 18,
  },
  chatButton: {
    backgroundColor: '#7BA05B',
    borderRadius: 25,
    paddingVertical: 12,
    alignItems: 'center',
  },
  chatButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: 'bold',
  },
  assessmentCard: {
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  assessmentGradient: {
    padding: 20,
    borderRadius: 20,
  },
  assessmentTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 12,
  },
  assessmentDescription: {
    fontSize: 14,
    color: '#FFFFFF',
    lineHeight: 20,
    marginBottom: 20,
    opacity: 0.95,
  },
  startAssessmentButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    paddingVertical: 12,
    alignItems: 'center',
  },
  startAssessmentButtonText: {
    color: '#4A9B8E',
    fontSize: 15,
    fontWeight: 'bold',
  },
  stressManagementCard: {
    backgroundColor: '#F0F8F0',
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  stressManagementContent: {
    padding: 20,
  },
  stressManagementHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  stressIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stressIconText: {
    fontSize: 20,
  },
  stressManagementTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  stressManagementDescription: {
    fontSize: 14,
    color: '#5D6D7E',
    lineHeight: 20,
    marginBottom: 18,
  },
  startJournalingButton: {
    backgroundColor: '#7BA05B',
    borderRadius: 25,
    paddingVertical: 12,
    alignItems: 'center',
  },
  startJournalingButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: 'bold',
  },
  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  modalBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  insightsModal: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 15,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  modalCloseButton: {
    padding: 8,
    borderRadius: 15,
    backgroundColor: '#F8F9FA',
  },
  modalCloseText: {
    fontSize: 16,
    color: '#7F8C8D',
    fontWeight: '600',
  },
  modalContent: {
    padding: 24,
  },
  modalInsightsAccent: {
    width: 4,
    height: 50,
    backgroundColor: '#7BA05B',
    borderRadius: 2,
    marginBottom: 16,
  },
  modalInsightsText: {
    fontSize: 16,
    color: '#5D6D7E',
    lineHeight: 24,
    marginBottom: 20,
    fontStyle: 'italic',
  },
  modalRecommendationsList: {
    paddingLeft: 0,
  },
  modalRecommendationItem: {
    fontSize: 16,
    color: '#5D6D7E',
    lineHeight: 26,
    marginBottom: 8,
  },
});

export default Dashboard;
